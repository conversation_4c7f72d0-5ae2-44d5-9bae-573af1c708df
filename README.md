# GoKarts.io 游戏网站

## 项目概述
GoKarts.io 是一个在线游戏网站，提供多种有趣的游戏体验。网站采用响应式设计，确保在PC和移动设备上都能良好显示。

## 页面结构
1. **页面顶部 (Header)**
   - 左侧：GoKarts.io 复古风格 Logo
   - 右侧：语言切换功能 (英文、中文、日文、韩文、西班牙语)

2. **页面主体 (Body)**
   - 左侧：游戏导航栏 (10个游戏超链接)
   - 中间：主游戏显示区域 (默认显示Epic Racing游戏，支持全屏功能)
   - 右侧：小游戏选择区 (10个小游戏，点击后在中间区域显示)

3. **页面底部内容**
   - 游戏介绍内容 (游戏截图、攻略、特点等)
   - 用户评论区
   - 常见问题解答 (FAQ)
   - 版权声明和联系信息

## 文件结构
- `index.html` - 网站主页
- `css/` - 样式文件目录
  - `styles.css` - 主样式文件
  - `responsive.css` - 响应式设计样式
- `js/` - JavaScript文件目录
  - `main.js` - 主要功能脚本
  - `language.js` - 多语言切换功能
- `images/` - 图片资源目录
- `fonts/` - 字体资源目录

## 技术栈
- HTML5
- CSS3 (Flexbox和Grid布局)
- JavaScript (原生JS，不使用框架)

## SEO优化
- 标题控制在40-60字符
- 描述控制在140-160字符
- 适当的标题标签使用 (h1, h2, h3)
- Canonical URL设置
- 核心关键词密度保持在3%-5%

## 响应式设计
网站采用响应式设计，确保在不同设备上都能良好显示：
- 桌面电脑
- 平板电脑
- 移动手机

## 多语言支持
网站支持以下语言：
- 英语 (默认)
- 中文
- 日文
- 韩文
- 西班牙语 